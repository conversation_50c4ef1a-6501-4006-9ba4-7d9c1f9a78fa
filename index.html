<!--
 * 版本: v1.2.0
 * 变更记录:
 * - v1.2.0 (2025-01-27): 修复页面底部空白区域问题，优化页面布局和加载体验
 * - v1.1.0 (2025-01-27): 调整页面背景颜色为深度适中的浅灰蓝色 #d4e6f1，优化视觉层次和对比度
 * - v1.0.0: 初始版本，完整的选址工具平台首页
 -->
<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>塔斯汀网络规划 - 沈浪</title>
    
    <!-- Favicon设置 -->
    <link rel="icon" href="logo.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="logo.svg">
    <link rel="shortcut icon" href="logo.svg" type="image/svg+xml">
    
    <!-- 性能优化元数据 -->
    <meta http-equiv="x-dns-prefetch-control" content="on">
    <link rel="dns-prefetch" href="https://cdn.tailwindcss.com">
    <link rel="dns-prefetch" href="https://use.fontawesome.com">
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">
    <link rel="dns-prefetch" href="//busuanzi.ibruce.info">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- TailwindCSS v3.4.1 CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="js/tailwind.config.js" defer></script>
    
    <!-- Font Awesome v5.11.2 CDN -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.11.2/css/all.css">

    <!-- Google Fonts for a more techy feel (Optional but recommended) -->
    <link href="https://fonts.googleapis.com/css2?family=Exo+2:wght@400;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- 字体加载优化 -->
    <style>
        /* 字体加载期间使用系统字体，防止布局跳动 */
        body {
            font-display: swap;
        }
    </style>

    <!-- 内联关键渲染路径所需的CSS -->
    <style>
        /* 关键样式 - 内联到HTML中减少阻塞请求 */
        html {
            height: 100%;
        }

        body {
            font-family: 'Exo 2', 'Noto Sans SC', sans-serif;
            background-color: #d4e6f1;
            /* 启用内容可视化优化 */
            content-visibility: auto;
            min-height: 100vh;
        }

        /* 滚动显示元素的初始状态 */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
            /* 提示浏览器该元素将发生变化 */
            will-change: opacity, transform;
        }

        /* 滚动显示元素的可见状态 */
        .scroll-reveal.is-visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* 高级卡片样式 - v2.0 增强悬浮交互效果 */
        .card {
            /* 升级阴影效果，使用多层阴影创造更精致的深度感 */
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.02),
                0 4px 16px rgba(0, 0, 0, 0.03),
                0 8px 32px rgba(79, 134, 198, 0.04),
                0 16px 64px rgba(79, 134, 198, 0.02);
            /* 改进过渡效果，使用更流畅的缓动曲线 */
            transition:
                transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                backdrop-filter 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            /* 增强边框效果 */
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(212, 230, 241, 0.18);
            backdrop-filter: blur(12px) saturate(1.1);
            position: relative;
            overflow: hidden;
            padding: 1rem;
            border-radius: 0.5rem;
            /* 提示浏览器该元素将发生变化 */
            will-change: transform, box-shadow, backdrop-filter;
            /* 添加微妙的内阴影增强质感 */
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.02),
                0 4px 16px rgba(0, 0, 0, 0.03),
                0 8px 32px rgba(79, 134, 198, 0.04),
                0 16px 64px rgba(79, 134, 198, 0.02),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        /* 桌面端卡片增强 - 增加内边距使卡片更宽，优化水平布局高度 */
        @media (min-width: 640px) {
            .card {
                padding: 1rem;
                min-height: 6rem;
            }
        }

        /* 移动端优化 - 降级悬浮效果 */
        @media (max-width: 767px) {
            .card {
                padding: 0.75rem;
                border-radius: 0.375rem;
                min-height: 4.5rem;
                backdrop-filter: blur(8px) saturate(1.05);
                background: rgba(212, 230, 241, 0.15);
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.02),
                    0 4px 12px rgba(0, 0, 0, 0.03),
                    inset 0 1px 0 rgba(255, 255, 255, 0.08);
                /* 移动端使用更简单的过渡 */
                transition:
                    transform 0.3s ease-out,
                    box-shadow 0.3s ease-out,
                    background-color 0.3s ease-out;
            }
        }

        @media (max-width: 400px) {
            .card {
                padding: 0.4rem;
                min-height: 4rem;
            }
        }

        .card:hover {
            /* 增强的悬停效果 - 更平滑的变换 */
            transform: translateY(-12px) scale(1.03) rotateX(2deg);
            /* 多层次阴影，创造更强烈的悬浮感 */
            box-shadow:
                0 8px 16px rgba(0, 0, 0, 0.04),
                0 16px 32px rgba(0, 0, 0, 0.06),
                0 24px 48px rgba(79, 134, 198, 0.12),
                0 32px 64px rgba(79, 134, 198, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                inset 0 -1px 0 rgba(79, 134, 198, 0.1);
            /* 悬停时更突出的玻璃拟态效果 */
            background: rgba(212, 230, 241, 0.28);
            backdrop-filter: blur(16px) saturate(1.2) brightness(1.05);
            /* 增强边框光晕 */
            border: 1px solid rgba(255, 255, 255, 0.4);
        }

        .card:active {
            transform: translateY(-6px) scale(0.98) rotateX(1deg);
            /* 点击时使用更紧凑但仍有质感的阴影效果 */
            box-shadow:
                0 4px 8px rgba(0, 0, 0, 0.06),
                0 8px 16px rgba(0, 0, 0, 0.04),
                0 12px 24px rgba(79, 134, 198, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            background: rgba(212, 230, 241, 0.32);
            backdrop-filter: blur(14px) saturate(1.15);
            transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card::before {
            content: '';
            position: absolute;
            inset: 0;
            /* 增强的梯度光晕效果 */
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.1) 25%,
                rgba(79, 134, 198, 0.15) 50%,
                rgba(120, 162, 210, 0.12) 75%,
                rgba(42, 84, 133, 0) 100%
            );
            opacity: 0;
            /* 更流畅的光效过渡 */
            transition:
                opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 1.0s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform: translateX(-120%) rotate(-10deg);
            pointer-events: none;
            z-index: 1;
            border-radius: inherit;
        }

        .card:hover::before {
            opacity: 0.8;
            transform: translateX(120%) rotate(10deg);
        }

        /* 增强的卡片边缘发光效果 */
        .card::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: inherit;
            padding: 1px;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.6) 0%,
                rgba(79, 134, 198, 0.4) 30%,
                rgba(120, 162, 210, 0.3) 70%,
                rgba(42, 84, 133, 0.25) 100%
            );
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            opacity: 0;
            transition:
                opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                filter 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            pointer-events: none;
            filter: blur(0.5px);
        }

        .card:hover::after {
            opacity: 0.9;
            filter: blur(0px);
        }

        /* 移动端悬浮效果降级 */
        @media (max-width: 767px) {
            .card::before {
                display: none;
            }

            .card:hover {
                /* 移动端使用更轻微的悬浮效果 */
                transform: translateY(-4px) scale(1.01);
                box-shadow:
                    0 4px 8px rgba(0, 0, 0, 0.04),
                    0 8px 16px rgba(0, 0, 0, 0.03),
                    0 12px 24px rgba(79, 134, 198, 0.06);
                background: rgba(212, 230, 241, 0.22);
                backdrop-filter: blur(10px) saturate(1.1);
            }

            .card:active {
                transform: translateY(-2px) scale(0.99);
                transition: all 0.1s ease-out;
            }
        }

        /* 添加微妙的呼吸效果（仅桌面端） */
        @media (min-width: 768px) {
            .card {
                animation: cardBreathing 6s ease-in-out infinite;
            }

            .card:hover {
                animation: none;
            }
        }

        @keyframes cardBreathing {
            0%, 100% {
                box-shadow:
                    0 2px 8px rgba(0, 0, 0, 0.02),
                    0 4px 16px rgba(0, 0, 0, 0.03),
                    0 8px 32px rgba(79, 134, 198, 0.04),
                    0 16px 64px rgba(79, 134, 198, 0.02),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
            }
            50% {
                box-shadow:
                    0 3px 10px rgba(0, 0, 0, 0.025),
                    0 6px 20px rgba(0, 0, 0, 0.035),
                    0 12px 40px rgba(79, 134, 198, 0.05),
                    0 20px 80px rgba(79, 134, 198, 0.025),
                    inset 0 1px 0 rgba(255, 255, 255, 0.12);
            }
        }

        /* 当模态框打开时防止body滚动 */
        .modal-open {
            overflow: hidden;
        }

        /* 4列布局容器宽度限制 - 确保每行恰好显示4个卡片 */
        #business-support, #geo-location, #data-analysis, #network-planning, #tutorials {
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
            min-height: 120px; /* 确保每个section有最小高度，避免加载时的空白 */
        }

        /* 在小屏幕上调整最大宽度以适应2列布局 */
        @media (max-width: 639px) {
            #business-support, #geo-location, #data-analysis, #network-planning, #tutorials {
                max-width: 400px;
            }
        }

        /* 导航栏描述文字样式 */
        header nav p {
            font-size: 0.875rem;
            color: #6b7280;
            line-height: 1.3;
            max-width: 280px;
            transition: color 0.3s ease;
        }

        header nav p:hover {
            color: #4f86c6;
        }

        /* 响应式优化 - 在较小的桌面屏幕上调整描述文字 */
        @media (min-width: 768px) and (max-width: 1024px) {
            header nav p {
                font-size: 0.8rem;
                max-width: 240px;
            }
        }

        /* 大屏幕上的优化 */
        @media (min-width: 1280px) {
            header nav p {
                font-size: 0.9rem;
                max-width: 320px;
            }
        }

        /* 增强的卡片点击涟漪效果 */
        .card-ripple {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(
                circle,
                rgba(58, 110, 165, 0.4) 0%,
                rgba(79, 134, 198, 0.3) 30%,
                rgba(120, 162, 210, 0.2) 60%,
                rgba(255, 255, 255, 0.1) 100%
            );
            transform: scale(0);
            animation: enhancedRipple 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            pointer-events: none;
            will-change: transform, opacity;
            filter: blur(0.5px);
        }

        @keyframes enhancedRipple {
            0% {
                transform: scale(0);
                opacity: 1;
                filter: blur(0.5px);
            }
            50% {
                transform: scale(2);
                opacity: 0.6;
                filter: blur(0px);
            }
            100% {
                transform: scale(4.5);
                opacity: 0;
                filter: blur(1px);
            }
        }





        /* 状态标签样式 */
        .status-badge {
            z-index: 10;
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 状态标签动画 */
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* 确保primary颜色可用 */
        .bg-primary\/15 {
            background-color: rgba(58, 110, 165, 0.15);
        }

        .text-primary-dark {
            color: #2A5485;
        }

        /* 蓝色状态标签样式 */
        .bg-blue-100 {
            background-color: rgba(219, 234, 254, 0.9);
        }

        .text-blue-800 {
            color: #1e40af;
        }

        /* 页面加载状态样式 */
        .page-loading .section-grid {
            opacity: 0.3;
            transition: opacity 0.5s ease;
        }

        .page-loading .section-grid::after {
            content: '正在加载内容...';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #6b7280;
            font-size: 0.875rem;
            pointer-events: none;
        }

        /* 页面加载完成后的样式 */
        body:not(.page-loading) .section-grid {
            opacity: 1;
        }
    </style>
    
    <!-- 非关键CSS延迟加载 -->
    <link rel="preload" href="css/styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="css/styles.css"></noscript>
    
    <!-- 配置文件 - 最先加载 (使用defer确保按顺序执行) -->
    <script src="js/config.js" defer></script>

    <!-- 数据源 - 其次加载 (使用defer确保按顺序执行) -->
    <script src="js/data.js" defer></script>

    <!-- 模块化JavaScript - 按照依赖顺序加载 (都使用defer确保按顺序执行) -->
    <script src="js/cardRenderer.js" defer></script>
    <script src="js/scrollAnimation.js" defer></script>
    <script src="js/modal.js" defer></script>
    <script src="js/statistics.js" defer></script>

    <!-- 不蒜子访问统计脚本 -->
    <script async src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script>

    <!-- 主应用 - 最后加载 -->
    <script src="js/app.js" defer></script>


    
    <!-- 备用初始化脚本 -->
    <script>
        // 备用数据初始化，防止数据加载失败
        document.addEventListener('DOMContentLoaded', function() {
            if (!window.contentData) {
                // 如果contentData不存在，创建应急数据
                window.contentData = {
                    businessSupport: [],
                    geoLocation: [],
                    dataAnalysis: [],
                    networkPlanning: [],
                    tutorials: []
                };
            }
        });
    </script>
</head>

<body class="bg-background-DEFAULT text-gray-700 page-loading">

    <!-- 页头 -->
    <header class="bg-background-DEFAULT sticky top-0 z-40">
        <nav class="container mx-auto px-4 py-2 flex items-center justify-between">
            <a href="#" class="flex items-center">
                <img src="logo.svg" alt="塔斯汀网络规划" class="h-8 mr-2" />
                <span class="text-lg font-bold text-primary-dark tracking-wider">选址工具平台</span>
            </a>
            <p class="hidden md:block text-sm text-gray-600 max-w-xs text-right leading-tight">
                助力企业精准定位潜力商圈，<br>
                提升选址决策效率与准确性！
            </p>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main class="flex-1">
        <!-- 主视觉部分 -->
        <section class="text-center py-6 md:py-8 px-4">
            <h1 class="text-3xl md:text-4xl font-extrabold text-primary-dark leading-tight">
                
            </h1>
        </section>

        <!-- 新店开发部分 -->
        <section id="business-support" class="container mx-auto px-3 sm:px-4 py-4 sm:py-5">
            <div class="section-header mb-4 md:mb-5 scroll-reveal">
                <h2 class="section-title text-lg sm:text-xl lg:text-2xl font-extrabold text-left">新店开发</h2>
            </div>
            <div id="business-support-grid" class="section-grid relative grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
                <!-- 新店开发卡片将由JavaScript动态生成 -->
            </div>
        </section>

        <!-- 地理位置部分 -->
        <section id="geo-location" class="container mx-auto px-3 sm:px-4 py-4 sm:py-5">
            <div class="section-header mb-4 md:mb-5 scroll-reveal">
                <h2 class="section-title text-lg sm:text-xl lg:text-2xl font-extrabold text-left">地理位置</h2>
            </div>
            <div id="geo-location-grid" class="section-grid relative grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
                <!-- 地理位置卡片将由JavaScript动态生成 -->
            </div>
        </section>

        <!-- 数据分析部分 -->
        <section id="data-analysis" class="container mx-auto px-3 sm:px-4 py-4 sm:py-5">
            <div class="section-header mb-4 md:mb-5 scroll-reveal">
                <h2 class="section-title text-lg sm:text-xl lg:text-2xl font-extrabold text-left">数据分析</h2>
            </div>
            <div id="data-analysis-grid" class="section-grid relative grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
                <!-- 数据分析卡片将由JavaScript动态生成 -->
            </div>
        </section>

        <!-- 点位分析部分 -->
        <section id="network-planning" class="container mx-auto px-3 sm:px-4 py-4 sm:py-5">
            <div class="section-header mb-4 md:mb-5 scroll-reveal">
                <h2 class="section-title text-lg sm:text-xl lg:text-2xl font-extrabold text-left">点位分析</h2>
            </div>
            <div id="network-planning-grid" class="section-grid relative grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
                <!-- 点位分析卡片将由JavaScript动态生成 -->
            </div>
        </section>

        <!-- 数据教程部分 -->
        <section id="tutorials" class="container mx-auto px-3 sm:px-4 py-4 sm:py-5">
            <div class="section-header mb-4 md:mb-5 scroll-reveal">
                <h2 class="section-title text-lg sm:text-xl lg:text-2xl font-extrabold text-left">数据教程</h2>
            </div>
            <div id="tutorials-grid" class="section-grid relative grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
                <!-- 教程卡片将由JavaScript动态生成 -->
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="text-center py-4 mt-4 bg-background-DEFAULT">

        <p class="text-sm text-gray-500">&copy; 2025 沈浪. <span class="text-primary font-medium">保留所有权利.</span></p>
    </footer>
    
    <!-- 模态框 -->
    <div id="modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
        <!-- 遮罩层 -->
        <div id="modal-overlay" class="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
        
        <!-- 模态框面板 -->
        <div id="modal-panel" class="relative bg-white rounded-lg p-5 m-3 max-w-2xl w-full transform transition-all duration-300 scale-95 opacity-0 shadow-xl">
            <button id="modal-close" class="absolute top-3 right-3 text-gray-400 hover:text-primary transition-colors">
                <i class="fas fa-times fa-lg"></i>
            </button>
            
            <div class="flex items-start space-x-4">
                <i id="modal-icon" class="text-primary-light text-4xl mt-1"></i>
                <div>
                    <h3 id="modal-name" class="text-xl font-bold text-gray-800 mb-1"></h3>
                    <p id="modal-description" class="text-gray-600 mb-4"></p>
                </div>
            </div>
            
            <div class="mb-4">
                <h4 class="font-semibold text-gray-800 mb-1">更新日志</h4>
                <pre id="modal-changelog" class="bg-gray-50 p-3 rounded-md text-gray-600 text-sm whitespace-pre-wrap font-mono border border-gray-200 max-h-60 overflow-y-auto leading-relaxed"></pre>
            </div>
            
            <a id="modal-link" href="#" target="_blank" rel="noopener noreferrer" class="inline-block w-full text-center bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded-lg transition-colors duration-300">
                访问工具 <i class="fas fa-external-link-alt ml-2"></i>
            </a>
        </div>
    </div>
    
    <!-- 备用初始化脚本 -->
    <script>
        // 备用初始化机制，确保在主应用失败时仍能正常工作
        window.addEventListener('load', function() {
            // 如果工具卡片没有渲染但数据存在，尝试手动初始化
            if (document.querySelectorAll('.card').length === 0 && window.contentData && window.CardRenderer) {
                window.CardRenderer.renderAllCards(window.contentData);

                if (window.ScrollAnimation) {
                    window.ScrollAnimation.init();
                }

                if (window.Modal) {
                    window.Modal.init();
                }
            }

            // 移除页面加载状态，显示内容
            setTimeout(function() {
                document.body.classList.remove('page-loading');
            }, 500);
        });
    </script>
</body>
</html>