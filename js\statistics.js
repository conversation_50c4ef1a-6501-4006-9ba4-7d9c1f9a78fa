/**
 * 访问统计模块
 * 负责集成和管理不蒜子访问统计功能
 * 版本: v2.0.0
 * 变更记录:
 * - v2.0.0 (2025-01-27): 重大简化 - 移除过度复杂的错误处理和重试机制，提升代码可维护性
 * - v1.1.0 (2025-07-27): 优化脚本检测逻辑，改进数据加载检测机制
 * - v1.0.0 (2025-07-27): 初始版本，集成不蒜子访问统计功能
 */

class Statistics {
    constructor() {
        this.config = window.AppConfig?.statistics || {};
        this.initialized = false;
        this.container = null;
    }

    /**
     * 初始化统计功能
     */
    init() {
        if (this.initialized) return;

        this.createContainer();
        this.waitForData();
        this.initialized = true;
    }

    /**
     * 创建统计显示容器
     */
    createContainer() {
        const footer = document.querySelector('footer');
        if (!footer) return;

        this.container = document.createElement('div');
        this.container.id = 'site-statistics';
        this.container.className = 'text-sm text-gray-500 mt-2';
        this.container.style.opacity = '0';
        this.container.innerHTML = this.buildStatisticsHTML();

        const copyright = footer.querySelector('p');
        if (copyright) {
            footer.insertBefore(this.container, copyright);
        } else {
            footer.appendChild(this.container);
        }
    }

    /**
     * 构建统计HTML
     */
    buildStatisticsHTML() {
        return `
            <span id="busuanzi_container_site_pv">
                本站总访问量 <span id="busuanzi_value_site_pv" class="font-medium text-primary">--</span> 次
            </span>
            |
            <span id="busuanzi_container_site_uv">
                独立访客数 <span id="busuanzi_value_site_uv" class="font-medium text-primary">--</span> 次
            </span>
        `;
    }

    /**
     * 等待数据加载
     */
    waitForData() {
        const checkData = () => {
            const sitePV = document.getElementById('busuanzi_value_site_pv');
            const siteUV = document.getElementById('busuanzi_value_site_uv');

            if ((sitePV && sitePV.textContent !== '--') ||
                (siteUV && siteUV.textContent !== '--')) {
                this.showStatistics();
            } else {
                setTimeout(checkData, 1000);
            }
        };

        setTimeout(checkData, 2000); // 延迟2秒开始检查
    }

    /**
     * 显示统计信息
     */
    showStatistics() {
        if (this.container) {
            this.container.style.opacity = '1';
        }
    }

    /**
     * 销毁统计功能
     */
    destroy() {
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        this.initialized = false;
        this.container = null;
    }
}

// 导出统计模块
window.Statistics = Statistics;
