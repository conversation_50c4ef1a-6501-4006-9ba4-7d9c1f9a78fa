<!--
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0 (2025-01-27): 创建颜色对比度测试页面，验证新背景色的视觉效果
 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景颜色对比测试</title>
    <style>
        body {
            font-family: 'Exo 2', 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .color-section {
            padding: 40px;
            margin: 20px 0;
            border-radius: 8px;
            text-align: center;
        }
        
        .old-color {
            background-color: #E4ECF7;
        }
        
        .new-color {
            background-color: #d4e6f1;
        }
        
        .test-card {
            background: rgba(212, 230, 241, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin: 20px auto;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .text-primary-dark {
            color: #2A5485;
        }
        
        .text-gray-700 {
            color: #374151;
        }
        
        .text-gray-600 {
            color: #4B5563;
        }
        
        h1, h2 {
            color: #2A5485;
            margin-bottom: 10px;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison > div {
            flex: 1;
        }
        
        @media (max-width: 768px) {
            .comparison {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <h1>背景颜色调整对比测试</h1>
    
    <div class="comparison">
        <div>
            <h2>原背景色 (#E4ECF7)</h2>
            <div class="color-section old-color">
                <h3 class="text-primary-dark">标题文字测试</h3>
                <p class="text-gray-700">这是正文内容，测试文字在原背景色下的可读性和对比度效果。</p>
                <p class="text-gray-600">这是辅助文字，颜色稍浅一些。</p>
            </div>
        </div>
        
        <div>
            <h2>新背景色 (#d4e6f1)</h2>
            <div class="color-section new-color">
                <h3 class="text-primary-dark">标题文字测试</h3>
                <p class="text-gray-700">这是正文内容，测试文字在新背景色下的可读性和对比度效果。</p>
                <p class="text-gray-600">这是辅助文字，颜色稍浅一些。</p>
            </div>
        </div>
    </div>
    
    <div class="new-color" style="padding: 40px;">
        <h2>新背景色下的卡片效果测试</h2>
        <div class="test-card">
            <h3 class="text-primary-dark">测试卡片</h3>
            <p class="text-gray-700">这是一个测试卡片，展示在新背景色下的玻璃拟态效果和文字对比度。</p>
            <p class="text-gray-600">卡片使用了半透明背景和模糊效果，营造现代化的视觉层次。</p>
        </div>
    </div>
    
    <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <h2>颜色信息</h2>
        <ul>
            <li><strong>原背景色:</strong> #E4ECF7 (RGB: 228, 236, 247)</li>
            <li><strong>新背景色:</strong> #d4e6f1 (RGB: 212, 230, 241)</li>
            <li><strong>主要文字色:</strong> #2A5485 (深蓝色)</li>
            <li><strong>正文文字色:</strong> #374151 (深灰色)</li>
            <li><strong>辅助文字色:</strong> #4B5563 (中灰色)</li>
        </ul>
        <p><strong>调整说明:</strong> 新背景色比原色更深一些，提供了更好的视觉层次感，同时保持了良好的文字对比度和现代化的设计风格。</p>
    </div>
</body>
</html>
